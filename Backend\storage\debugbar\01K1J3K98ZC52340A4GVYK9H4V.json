{"__meta": {"id": "01K1J3K98ZC52340A4GVYK9H4V", "datetime": "2025-08-01 07:02:36", "utime": **********.191979, "method": "GET", "uri": "/admin/dashboard/stats?type=total-sales", "ip": "127.0.0.1"}, "modules": {"count": 3, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 0.37, "duration_str": "370ms", "connection": "mlk"}]}, {"name": "Webkul\\Sales", "models": ["Webkul\\Sales\\Models\\Order (2)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-06-02 00:00:00' and '2025-07-02 00:00:00'", "duration": 2.39, "duration_str": "2.39s", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-07-02 00:00:00' and '2025-08-01 07:02:36'", "duration": 0.28, "duration_str": "280ms", "connection": "mlk"}, {"sql": "select sum(base_grand_total_invoiced - base_grand_total_refunded) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-06-02 00:00:00' and '2025-07-02 00:00:00'", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select sum(base_grand_total_invoiced - base_grand_total_refunded) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-07-02 00:00:00' and '2025-08-01 07:02:36'", "duration": 0.4, "duration_str": "400ms", "connection": "mlk"}, {"sql": "select DAYOFYEAR(created_at) AS date, SUM(base_grand_total_invoiced - base_grand_total_refunded) AS total, COUNT(*) AS count from `orders` where `channel_id` in (1) and `created_at` between '2025-07-02 00:00:00' and '2025-08-01 07:02:36' group by `date`", "duration": 0.43, "duration_str": "430ms", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.45, "duration_str": "450ms", "connection": "mlk"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 3.28, "duration_str": "3.28s", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754028155.710701, "end": **********.197944, "duration": 0.4872429370880127, "duration_str": "487ms", "measures": [{"label": "Booting", "start": 1754028155.710701, "relative_start": 0, "end": **********.076897, "relative_end": **********.076897, "duration": 0.****************, "duration_str": "366ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.076908, "relative_start": 0.****************, "end": **********.197946, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "121ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.096584, "relative_start": 0.***************, "end": **********.101242, "relative_end": **********.101242, "duration": 0.0046579837799072266, "duration_str": "4.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.190125, "relative_start": 0.*****************, "end": **********.190463, "relative_end": **********.190463, "duration": 0.0003380775451660156, "duration_str": "338μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 12, "nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00912, "accumulated_duration_str": "9.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.142999, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 6.579}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.147679, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 6.579, "width_percent": 2.961}, {"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.155026, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 9.539, "width_percent": 4.057}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.15688, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 13.596, "width_percent": 2.522}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.158615, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 16.118, "width_percent": 1.754}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.1641212, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 17.873, "width_percent": 4.934}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.167699, "duration": 0.00328, "duration_str": "3.28ms", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 22.807, "width_percent": 35.965}, {"sql": "select count(*) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-06-02 00:00:00' and '2025-07-02 00:00:00'", "type": "query", "params": [], "bindings": [1, "2025-06-02 00:00:00", "2025-07-02 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 76}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 36}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 107}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1723392, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "Sale.php:76", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FSale.php&line=76", "ajax": false, "filename": "Sale.php", "line": "76"}, "connection": "mlk", "explain": null, "start_percent": 58.772, "width_percent": 26.206}, {"sql": "select count(*) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-07-02 00:00:00' and '2025-08-01 07:02:36'", "type": "query", "params": [], "bindings": [1, "2025-07-02 00:00:00", "2025-08-01 07:02:36"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 76}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 37}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 107}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1764162, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Sale.php:76", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 76}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FSale.php&line=76", "ajax": false, "filename": "Sale.php", "line": "76"}, "connection": "mlk", "explain": null, "start_percent": 84.978, "width_percent": 3.07}, {"sql": "select sum(base_grand_total_invoiced - base_grand_total_refunded) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-06-02 00:00:00' and '2025-07-02 00:00:00'", "type": "query", "params": [], "bindings": [1, "2025-06-02 00:00:00", "2025-07-02 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 175}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 130}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 108}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.178442, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Sale.php:175", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 175}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FSale.php&line=175", "ajax": false, "filename": "Sale.php", "line": "175"}, "connection": "mlk", "explain": null, "start_percent": 88.048, "width_percent": 2.851}, {"sql": "select sum(base_grand_total_invoiced - base_grand_total_refunded) as aggregate from `orders` where `channel_id` in (1) and `created_at` between '2025-07-02 00:00:00' and '2025-08-01 07:02:36'", "type": "query", "params": [], "bindings": [1, "2025-07-02 00:00:00", "2025-08-01 07:02:36"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 175}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 131}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 108}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.179759, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Sale.php:175", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 175}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FSale.php&line=175", "ajax": false, "filename": "Sale.php", "line": "175"}, "connection": "mlk", "explain": null, "start_percent": 90.899, "width_percent": 4.386}, {"sql": "select DAYOFYEAR(created_at) AS date, SUM(base_grand_total_invoiced - base_grand_total_refunded) AS total, COUNT(*) AS count from `orders` where `channel_id` in (1) and `created_at` between '2025-07-02 00:00:00' and '2025-08-01 07:02:36' group by `date`", "type": "query", "params": [], "bindings": [1, "2025-07-02 00:00:00", "2025-08-01 07:02:36"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 623}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 225}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 212}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 109}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 51}], "start": **********.1836128, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Sale.php:623", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Sale.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Sale.php", "line": 623}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FSale.php&line=623", "ajax": false, "filename": "Sale.php", "line": "623"}, "connection": "mlk", "explain": null, "start_percent": 95.285, "width_percent": 4.715}]}, "models": {"data": {"Webkul\\Sales\\Models\\Order": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FSales%2Fsrc%2FModels%2FOrder.php&line=1", "ajax": false, "filename": "Order.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/admin/dashboard/stats?type=total-sales", "action_name": "admin.dashboard.stats", "controller_action": "Webkul\\Admin\\Http\\Controllers\\DashboardController@stats", "uri": "GET admin/dashboard/stats", "controller": "Webkul\\Admin\\Http\\Controllers\\DashboardController@stats<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FDashboardController.php&line=49\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/dashboard", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FDashboardController.php&line=49\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/DashboardController.php:49-57</a>", "middleware": "web, admin_locale, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "489ms", "peak_memory": "40MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1328240357 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"11 characters\">total-sales</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1328240357\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-625510972 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-625510972\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1605595550 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IkQzcmd6RU9wV1RNZTJRbGI2aTQ3bnc9PSIsInZhbHVlIjoiVnFFcUROSWtNb0FLeU54eVVYSnZuckVVVFBsdDlmS2lJNGh2dmZHempzKzRlWXNVSUJTejZGSW9SZzVrVGVPZjhZWHhDT01iUGVzajdvd0piWHJzMTJ1VW1XT2gzcW9vRnlRcnFyYythcmxUQWx0ODlhMk9HTU9VNjdwUjFYaDMiLCJtYWMiOiIxYzQ1M2JjZGE3MzVhMTA3MGNkYjQ1ZmNmNTc4OGVkODE0NWE4YzNjZDlmYTI3NjA1Y2RjNDA2ZGUwMmZlMzExIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IjVoVGhpRmRNamE0UjZRelZGUFFtV3c9PSIsInZhbHVlIjoib3VrR05LZHYwbG13Q0FhSjE5NDBmSTlnYmtKbXJ5Z1N6eDdiOEE3UU9iRW4wSWF3bGdTUXovOWNjR2w2YTdZTzcyeW9UeEo4Z1hkcUUrQ0d4U2ZuT3JGNTAzNk5TOUgwTURDSzNqaVZCVE1iaHhkand5NjFSZVo3WElZS09sb2YiLCJtYWMiOiJjNWUyMTA2NjViNDJlODk5MzlkOTc3OGY3MmQ1OGJjNjBlZmYzN2U4YWE0MmQ0ZDIxMTBmMGM4Y2QyNDU1ZTNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://mlk.test/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkQzcmd6RU9wV1RNZTJRbGI2aTQ3bnc9PSIsInZhbHVlIjoiVnFFcUROSWtNb0FLeU54eVVYSnZuckVVVFBsdDlmS2lJNGh2dmZHempzKzRlWXNVSUJTejZGSW9SZzVrVGVPZjhZWHhDT01iUGVzajdvd0piWHJzMTJ1VW1XT2gzcW9vRnlRcnFyYythcmxUQWx0ODlhMk9HTU9VNjdwUjFYaDMiLCJtYWMiOiIxYzQ1M2JjZGE3MzVhMTA3MGNkYjQ1ZmNmNTc4OGVkODE0NWE4YzNjZDlmYTI3NjA1Y2RjNDA2ZGUwMmZlMzExIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1605595550\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1887352605 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z2tELH6Xpg9QukN3zvo9zCxcNcTdoGLPzaDEBz2i</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">cw91O4N2MCy7mfLhm4HeW8HqsUoEMYzWaDajLnMf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1887352605\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-544231104 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Aug 2025 06:02:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-544231104\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-315725536 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">z2tELH6Xpg9QukN3zvo9zCxcNcTdoGLPzaDEBz2i</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://mlk.test/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-315725536\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/admin/dashboard/stats?type=total-sales", "action_name": "admin.dashboard.stats", "controller_action": "Webkul\\Admin\\Http\\Controllers\\DashboardController@stats"}, "badge": null}}