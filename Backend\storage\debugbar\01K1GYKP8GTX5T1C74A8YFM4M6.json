{"__meta": {"id": "01K1GYKP8GTX5T1C74A8YFM4M6", "datetime": "2025-07-31 20:16:12", "utime": **********.17684, "method": "GET", "uri": "/cache/medium/product/178/C9CAErOffwfkXBRYkun6XkFlJCeaZeIGTGhNCXZB.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753989371.935484, "end": **********.186761, "duration": 0.25127696990966797, "duration_str": "251ms", "measures": [{"label": "Booting", "start": 1753989371.935484, "relative_start": 0, "end": **********.153638, "relative_end": **********.153638, "duration": 0.****************, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.153648, "relative_start": 0.*****************, "end": **********.186762, "relative_end": 1.1920928955078125e-06, "duration": 0.*****************, "duration_str": "33.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.165847, "relative_start": 0.***************, "end": **********.169535, "relative_end": **********.169535, "duration": 0.0036878585815429688, "duration_str": "3.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.175238, "relative_start": 0.*****************, "end": **********.175375, "relative_end": **********.175375, "duration": 0.00013709068298339844, "duration_str": "137μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.175388, "relative_start": 0.*****************, "end": **********.1754, "relative_end": **********.1754, "duration": 1.1920928955078125e-05, "duration_str": "12μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/178/C9CAErOffwfkXBRYkun6XkFlJCeaZeIGTGhNCXZB.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "252ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2027169442 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2027169442\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-147507403 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-147507403\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1502099725 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IlY5V1E1T21WdW00Sm5TSXQwTXVJdGc9PSIsInZhbHVlIjoiNXp0RDRuZW9NMmJxMGUxaS90bVdHTERpaTV4UlVLR3BlblpxRDhxVUlLdUdQV0QrTSswQlcxeUxPbWhCQ3VyM1dvL0ZpcjF3Um8xcWRqdGd2SWJSMnFkSFQwU2kzOTNqZGUyMTZGck80bGV5N2dQcnU4TVYyR1hzOGlKdjlFMDkiLCJtYWMiOiI2NTU5YzA2YmQxNWU4MDY3MmZmODRlNWQzMmZiZTczNjc2MTJhNGM0YzQyNDNjMmNhNjc1MmNiMmNmMGZhZjQ0IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6ImJzRmtVMnNOZzArMmNESE5PeXdwY0E9PSIsInZhbHVlIjoiS0ZjbEZWNS9rQzNTaE1CaDh1bm5yV0hKdnZlemExZ1k0YXEzclg2VUhwUk5DUlJ0a1ovWTRrZEpaKzk4NW1NSFNwaGUxd091allNMlZBUFpsZHl4K21UN3hNcXNjbGxOandLRnZjVVhpcWErcVNQdGdFT0VyaVNIdUI5QW9aVWYiLCJtYWMiOiIwNzIyNDBhMWMxZjIwMDZhMGU5OGNlNjFkOWFhMTMxYWIyNGQyZGRjNDQxZDhmNWVmZmVlOGE5Y2Q5ZDliNmIyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">http://mlk.test/web</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1502099725\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-788608860 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlY5V1E1T21WdW00Sm5TSXQwTXVJdGc9PSIsInZhbHVlIjoiNXp0RDRuZW9NMmJxMGUxaS90bVdHTERpaTV4UlVLR3BlblpxRDhxVUlLdUdQV0QrTSswQlcxeUxPbWhCQ3VyM1dvL0ZpcjF3Um8xcWRqdGd2SWJSMnFkSFQwU2kzOTNqZGUyMTZGck80bGV5N2dQcnU4TVYyR1hzOGlKdjlFMDkiLCJtYWMiOiI2NTU5YzA2YmQxNWU4MDY3MmZmODRlNWQzMmZiZTczNjc2MTJhNGM0YzQyNDNjMmNhNjc1MmNiMmNmMGZhZjQ0IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImJzRmtVMnNOZzArMmNESE5PeXdwY0E9PSIsInZhbHVlIjoiS0ZjbEZWNS9rQzNTaE1CaDh1bm5yV0hKdnZlemExZ1k0YXEzclg2VUhwUk5DUlJ0a1ovWTRrZEpaKzk4NW1NSFNwaGUxd091allNMlZBUFpsZHl4K21UN3hNcXNjbGxOandLRnZjVVhpcWErcVNQdGdFT0VyaVNIdUI5QW9aVWYiLCJtYWMiOiIwNzIyNDBhMWMxZjIwMDZhMGU5OGNlNjFkOWFhMTMxYWIyNGQyZGRjNDQxZDhmNWVmZmVlOGE5Y2Q5ZDliNmIyIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-788608860\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-9306108 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">6790</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">fe98aceff7b794c22782ef99966c688a</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 19:16:12 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9306108\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1001010756 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1001010756\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://mlk.test/cache/medium/product/178/C9CAErOffwfkXBRYkun6XkFlJCeaZeIGTGhNCXZB.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}